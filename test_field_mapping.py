import json
import sqlite3

# Your JSON data
json_data = {
    "data": {
        "structure": {
            "master": ["Key","InvoiceDate","CustomerCode","Buyer","CustomerName","CustomerTaxCode","CustomerType","Address","PhoneNumber","FaxNumber","EmailDeliver","BankAccount","BankName","PaymentMethod","Currency","ExchangeRate","Amount","TotalAmount","TaxRate","TaxAmount","TaxAmount5","TaxAmount10","AmountInWords","HumanName","DiscountAmount","PromotionAmount","Note","VoucherType","IDCardNo","PassportNo","BuyerUnit"],
            "detail": ["ItemCode","ItemName","UOM","IsPromotion","Quantity","Price","Amount","TaxRate","TaxAmount","DiscountAmount","ProcessType"]
        },
        "invoices": [{
            "master": ["A000005943HDA","07/08/2025","**********","","CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ PHÚ HƯNG","**********",<PERSON>,"<PERSON><PERSON><PERSON> <PERSON><PERSON> 4, Tầng 21, Tòa Nhà Phú M<PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON> 8 <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>ng <PERSON><PERSON>n <PERSON>h<PERSON><PERSON>, <PERSON>hu <PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, <PERSON>u<PERSON>n 7, <PERSON>h<PERSON>nh <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>t <PERSON>","0794889930",<PERSON>,"wins<PERSON><EMAIL>","","","TM/CK","VND",1.000000000000,800000.00,864000.00,8.00,64000.00,None,None,"Tám trăm sáu mươi bốn nghìn đồng chẵn","FAST",0.00,0.00,"0ÿ",None,"","",""],
            "detail": [[None,"Carrot Bread","Phần",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"],[None,"SERVICE FEE MAC LEVEL 1 (661-17548)","Cái",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"]]
        }]
    },
    "voucherBook": "BMRK25",
    "adjustmentType": 0
}

# Extract data
structure = json_data['data']['structure']
invoice = json_data['data']['invoices'][0]
master_header = structure['master']
master_values = invoice['master']
detail_header = structure['detail']
detail_rows = invoice['detail']

print("=== PHÂN TÍCH DỮ LIỆU JSON ===")
print("\n1. CẤU TRÚC MASTER:")
master_data = dict(zip(master_header, master_values))
for key, value in master_data.items():
    print(f"  {key}: {value}")

print("\n2. CẤU TRÚC DETAIL:")
print(f"  Header: {detail_header}")
for i, row in enumerate(detail_rows):
    print(f"  Row {i+1}: {dict(zip(detail_header, row))}")

print("\n=== KIỂM TRA VẤN ĐỀ MAPPING ===")

# Simulate the field mapping issue
conn = sqlite3.connect(':memory:')
cursor = conn.cursor()

# Create tables with English field names (as they appear in JSON)
master_cols = [col.replace(' ', '_') for col in master_header]
master_create_sql = f"CREATE TABLE master ({', '.join([f'{col} TEXT' for col in master_cols])})"
print(f"\n3. SQL TẠO BẢNG MASTER:")
print(f"  {master_create_sql}")

cursor.execute(master_create_sql)
cursor.execute(f"INSERT INTO master VALUES ({', '.join(['?' for _ in master_values])})", master_values)

detail_cols = [col.replace(' ', '_') for col in detail_header]
detail_create_sql = f"CREATE TABLE details ({', '.join([f'{col} TEXT' for col in detail_cols])})"
print(f"\n4. SQL TẠO BẢNG DETAILS:")
print(f"  {detail_create_sql}")

cursor.execute(detail_create_sql)
for row in detail_rows:
    cursor.execute(f"INSERT INTO details VALUES ({', '.join(['?' for _ in row])})", row)

print("\n5. KIỂM TRA CÁC LUẬT VALIDATION:")

# Test validation rules that would fail due to field name mismatch
test_rules = [
    "SELECT 1 FROM master WHERE Tổng_tiền_thanh_toán < 0",  # Vietnamese field name
    "SELECT 1 FROM master WHERE TotalAmount < 0",           # English field name
    "SELECT 1 FROM master WHERE Tên_đơn_vị_mua_hàng IS NULL OR Tên_đơn_vị_mua_hàng = ''",  # Vietnamese
    "SELECT 1 FROM master WHERE CustomerName IS NULL OR CustomerName = ''"  # English
]

for i, rule in enumerate(test_rules, 1):
    try:
        cursor.execute(rule)
        result = cursor.fetchone()
        print(f"  Rule {i}: ✓ THÀNH CÔNG - {rule}")
        print(f"    Kết quả: {result}")
    except sqlite3.Error as e:
        print(f"  Rule {i}: ✗ LỖI - {rule}")
        print(f"    Lỗi: {e}")

conn.close()

print("\n=== KẾT LUẬN ===")
print("Vấn đề: Các luật validation sử dụng tên trường tiếng Việt,")
print("nhưng dữ liệu JSON sử dụng tên trường tiếng Anh.")
print("Cần có mapping giữa tên trường JSON và tên trường trong SQL rules.")
