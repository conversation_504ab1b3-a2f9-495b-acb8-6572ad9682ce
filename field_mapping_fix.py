"""
<PERSON><PERSON><PERSON><PERSON> pháp sửa lỗi mapping trường dữ liệu trong einvoice_debugger_tab.py

Vấn đề: 
- JSON data sử dụng tên trường tiếng <PERSON>h (VD: TotalAmount, CustomerName)
- SQL validation rules sử dụng tên trường tiếng Vi<PERSON> (VD: Tổng_tiền_thanh_toán, Tên_đơn_vị_mua_hàng)
- Cần mapping giữa JSON field names và SQL column names

Giải pháp:
1. Tạo mapping từ JSON field names sang Vietnamese field names cho SQL
2. Sửa đổi _parse_json_input_data để sử dụng mapping này
3. Đảm bảo SQL validation rules hoạt động đúng
"""

import json

# Mapping từ JSON field names sang Vietnamese field names cho SQL validation
JSON_TO_SQL_MAPPING = {
    # Master fields mapping
    "Key": "Khóa",
    "InvoiceDate": "<PERSON><PERSON><PERSON>_hóa_đơn", 
    "CustomerCode": "Mã_khách_hàng",
    "Buyer": "Tên_người_mua_hàng",
    "CustomerName": "Tên_đơn_vị_mua_hàng",
    "CustomerTaxCode": "Mã_số_thuế_người_mua",
    "CustomerType": "Loại_khách_hàng",
    "Address": "Địa_chỉ_người_mua_hàng",
    "PhoneNumber": "Điện_thoại_người_mua_hàng",
    "FaxNumber": "Số_fax_người_mua_hàng",
    "EmailDeliver": "Danh_sách_email_người_mua_hàng_nhận_thông_báo_phát_hành",
    "BankAccount": "Tài_khoản_ngân_hàng_người_mua",
    "BankName": "Tên_ngân_hàng_người_mua",
    "PaymentMethod": "Hình_thức_thanh_toán",
    "Currency": "Mã_ngoại_tệ",
    "ExchangeRate": "Tỷ_giá",
    "Amount": "Tổng_tiền_hàng",
    "TotalAmount": "Tổng_tiền_thanh_toán",
    "TaxRate": "Thuế_suất",
    "TaxAmount": "Tổng_tiền_thuế",
    "TaxAmount5": "Tổng_tiền_thuế_5",
    "TaxAmount10": "Tổng_tiền_thuế_10",
    "AmountInWords": "Số_tiền_đọc_bằng_chữ_Tổng_thanh_toán",
    "HumanName": "Tên_người",
    "DiscountAmount": "Tổng_tiền_chiết_khấu",
    "PromotionAmount": "Tổng_tiền_hàng_khuyến_mãi",
    "Note": "Ghi_chú",
    "VoucherType": "Loại_phiếu",
    "IDCardNo": "Số_CMND",
    "PassportNo": "Số_hộ_chiếu",
    "BuyerUnit": "Đơn_vị_người_mua",
    
    # Detail fields mapping
    "ItemCode": "Mã_vật_tư",
    "ItemName": "Tên_vật_tư", 
    "UOM": "Đơn_vị_tính",
    "IsPromotion": "Khuyến_mãi",
    "Quantity": "Số_lượng",
    "Price": "Giá",
    "TaxAmount": "Tiền_thuế",  # Note: Same name as master but different context
    "DiscountAmount": "Tiền_chiết_khấu",  # Note: Same name as master but different context
    "ProcessType": "Loại_xử_lý"
}

def create_mapping_functions():
    """Tạo các hàm mapping cần thiết"""
    
    def map_json_to_sql_fields(data_dict: dict, is_master: bool = True) -> dict:
        """
        Map JSON field names to Vietnamese SQL field names
        
        Args:
            data_dict: Dictionary with JSON field names as keys
            is_master: True for master data, False for detail data
            
        Returns:
            Dictionary with Vietnamese field names as keys
        """
        mapped_dict = {}
        for json_key, value in data_dict.items():
            # Try to find mapping, fallback to original key if not found
            sql_key = JSON_TO_SQL_MAPPING.get(json_key, json_key)
            mapped_dict[sql_key] = value
        return mapped_dict
    
    def map_detail_rows(detail_rows: list) -> list:
        """
        Map detail rows from JSON field names to Vietnamese SQL field names
        
        Args:
            detail_rows: List of dictionaries with JSON field names
            
        Returns:
            List of dictionaries with Vietnamese field names
        """
        mapped_rows = []
        for row in detail_rows:
            mapped_row = map_json_to_sql_fields(row, is_master=False)
            mapped_rows.append(mapped_row)
        return mapped_rows
    
    return map_json_to_sql_fields, map_detail_rows

def test_mapping():
    """Test the mapping with sample data"""
    
    # Sample data from the user's JSON
    sample_master = {
        "Key": "A000005943HDA",
        "InvoiceDate": "07/08/2025", 
        "CustomerCode": "0102594384",
        "CustomerName": "CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ PHÚ HƯNG",
        "TotalAmount": 864000.00,
        "Amount": 800000.00,
        "TaxAmount": 64000.00
    }
    
    sample_detail = [
        {
            "ItemCode": None,
            "ItemName": "Carrot Bread", 
            "UOM": "Phần",
            "Quantity": 20.0,
            "Price": 20000.0,
            "Amount": 400000.0,
            "TaxAmount": 32000.0
        }
    ]
    
    map_json_to_sql_fields, map_detail_rows = create_mapping_functions()
    
    print("=== TEST MAPPING ===")
    print("\n1. MASTER DATA MAPPING:")
    mapped_master = map_json_to_sql_fields(sample_master, is_master=True)
    for original_key, value in sample_master.items():
        mapped_key = JSON_TO_SQL_MAPPING.get(original_key, original_key)
        print(f"  {original_key} -> {mapped_key}: {value}")
    
    print("\n2. DETAIL DATA MAPPING:")
    mapped_detail = map_detail_rows(sample_detail)
    for original_key in sample_detail[0].keys():
        mapped_key = JSON_TO_SQL_MAPPING.get(original_key, original_key)
        print(f"  {original_key} -> {mapped_key}")
    
    print("\n3. MAPPED MASTER RESULT:")
    for key, value in mapped_master.items():
        print(f"  {key}: {value}")
        
    print("\n4. MAPPED DETAIL RESULT:")
    for key, value in mapped_detail[0].items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_mapping()
